{% if logo %}
    <div class="logotype_widget">
        <img src="{{ logo.servingUrl }}" alt="Logo">
    </div>
{% endif %}

{% if booking_steps %}
    <div class="booking_steps">
        <div class="content_wrapper">
            <div class="step step_flight hidden">
                <div class="number_step">
                    <span>1</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_select_origin_airport }}
                </span>
            </div>
            <div class="step step_2">
                <div class="number_step">
                    <span>1</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_seleccionar_fechas }}
                </span>
            </div>
            <div class="step step_3">
                <div class="number_step">
                    <span>2</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_seleccionar_ocupacion }}
                </span>
            </div>
        </div>
    </div>
{% endif %}

{% if widget_buttons %}
    <div class="widget_buttons">
        <div class="content_wrapper">
            {% for button in widget_buttons %}
                <a class="button" href="{{ button.external_link|safe }}" {% if button.datalayer_name %}data-datalayer-name="{{ button.datalayer_name|safe }}"{% endif %} target="_blank">
                    {{ button.description|safe }}
                </a>
            {% endfor %}
        </div>
    </div>
{% endif %}

{% if forcedStartDate %}
<input type="hidden" id="forcedStartDate" name="forcedStartDate" value="{{forcedStartDate}}">
{% endif %}