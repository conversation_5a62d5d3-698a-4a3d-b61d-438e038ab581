<link href="/static_1/css/utils/users/club_toggle.css?v=@@automatic_version@@" rel="stylesheet" />

<style>
    {% if club_config %}
        {% if club_config.color1 %}
            :root {
                --club-main-color: {{ club_config.color1|safe }};
            }
        {% endif %}

        {% if club_config.color2 %}
            :root {
                --club-secondary-color: {{ club_config.color2|safe }}!important;
            }
        {% endif %}

        {% if club_config.color_text %}
            :root {
                --club-text-color: {{ club_config.color_text|safe }}!important;
            }
        {% endif %}
    {% endif %}
</style>

<div class="banner-toggle-club">
    {% if banner_club %}
        <div class="logotype-wrapper">
            <img src="{{ banner_club.servingUrl }}" alt="logotype">
        </div>
    {% endif %}

    <div class="main-content">
        <a class="see-advantages"><i class="fa-light fa-circle-info" aria-hidden="true"></i></a>
        <span class="main-text">
            {{ main_description.description|safe }}
        </span>
    </div>

    <div class="toggle-discount-wrapper {% if disable_club_discount and is_booking3 %}discount-disabled{% endif %}"><span class="label-text">{{ T_descuento_activo }}</span><span
            class="label-text label-disabled-text">{{ T_descuento_inactivo }}</span>
        <div class="discount-switch-wrapper">
            <div class="switch">
                <input class="checkbox" type="checkbox" checked>
                <div class="knobs"></div>
                <div class="layer"></div>
            </div>
        </div>
    </div>
</div>

<div class="club_send_password_wrapper" style="display: none">
    <form id="club-send-password-form" name="club-send-password-form" onsubmit="return false">
        <input type="hidden" name="language" value="{{ complete_language }}">
        <div class="input_wrapper input_center">
            <p class="old_club_email" style="display: none">{{ T_club_special_popup_2|safe }}</p>
            <div class="input">
                <input type="text" name="club_email" id="club_email" placeholder="{{ T_introduzca_email }}">
            </div>
        </div>
        <div class="btn_wrapper">
            <div id="submit_recovery_club" class="btn">{{ T_enviar }}</div>
        </div>
    </form>
</div>

<script>
    $(function() {
        const checkboxDiscount = $('.banner-toggle-club .discount-switch-wrapper input.checkbox');
        $('#rooms_b1_wrapper .listadoHabsTarifas .discount_disabled, #step-3 .lock_rates_wrapper').click(function() {
            checkboxDiscount.prop('checked', 'checked');
            checkboxDiscount.trigger('change');
        });

        checkboxDiscount.change(function() {
            const $checkbox = $(this);
            const is_checked = $checkbox.is(':checked');

            sendEventToggleDiscount(is_checked);
            toggleDiscount();

            const current_url = window.location.href;
            const choice_club_discount = is_checked ? 'active' : 'disabled';

            if (current_url.includes('/booking3')) {
                // Reload page with URL parameter
                const url = new URL(current_url);
                const params = new URLSearchParams(url.search);

                if (params.has('choice_club_discount')) {
                    params.set('choice_club_discount', choice_club_discount);
                } else {
                    params.append('choice_club_discount', choice_club_discount);
                }

                openBookingSearchPopup_v2();
                url.search = params.toString();
                window.location.href = url.toString();
            } else if (current_url.includes('/booking1')) {
                if ($("input[name='selectedUUID']").val()) {
                    if (confirm("Se va a recargar la búsqueda. ¿Desea continuar?")) {
                        performBookingSearchWithClubDiscountState(choice_club_discount);
                    } else {
                        // If the user doesn't want to reload the page, we need to revert the checkbox state.
                        toggleDiscount();
                        $checkbox.prop('checked', !is_checked);
                    }
                } else {
                    // Maintain the club discount status
                    $("form#selectionForm").find('input[name="choice_club_discount"]').val(choice_club_discount);

                    // Perform changes without leaving the page
                    _check_empty_rooms();
                }
            } else {
                // Repeat booking search with parameter
                performBookingSearchWithClubDiscountState(choice_club_discount);
            }
        });

        {% if disable_club_discount %}
            checkboxDiscount.prop('checked', false);
            disableClubDiscount();
            {% if not is_booking3 %}
                checkboxDiscount.trigger('change');
            {% endif %}
        {% else %}
            activateClubDiscount();
        {% endif %}

        $("#club-send-password-form").find("#submit_recovery_club").click(function() {
            const email_input = $(".club_send_password_wrapper #club_email");
            const username = email_input.val();
            const language = $('.club_send_password_wrapper input[name="language"]').val();

            $("#club-send-password-form").validate({
                rules: {
                    club_email: {
                        required: true,
                        email: true
                    }
                },

                messages: {
                    club_email: {
                        required: $.i18n._("T_campo_obligatorio"),
                        email: $.i18n._("T_campo_valor_invalido")
                    }
                },

                highlight: function (input) {
                    $(input).css('border', '1px solid red');
                    $(input).closest(".input_block").addClass("error");
                    return;
                },

                unhighlight: function (input) {
                    $(input).css('border', '0');
                    $(input).closest(".input_block").removeClass("error");
                }
            });

            if ($("#club-send-password-form").valid()) {
                recoverPassword(username, language);
            }
        });
    });

    function toggleDiscount() {
        $('.toggle-discount-wrapper').toggleClass('discount-disabled');
        $('#rooms_b1_wrapper tr.rate_tr_element').toggle();
        $('#rooms_b1_wrapper tr.regimen_tr_element').toggle();
    }

    function activateClubDiscount() {
        $('#selectionForm input[name="choice_club_discount"]').remove();
        $('#selectionForm').append('<input type="hidden" name="choice_club_discount" value="active">');
        $('.contDescHabitacion.with_rates_dropdown .room_from_price').hide();
        $('.contDescHabitacion.with_rates_dropdown .room_from_price.club').show();
        $('#rooms_b1_wrapper tr.rate_tr_element, #rooms_b1_wrapper tr.regimen_tr_element').hide();
        $('#rooms_b1_wrapper tr.rate_tr_element.rate_for_users, #rooms_b1_wrapper tr.regimen_tr_element.regimen_for_users').show();
    }

    function disableClubDiscount() {
        $('#selectionForm input[name="choice_club_discount"]').remove();
        $('#selectionForm').append('<input type="hidden" name="choice_club_discount" value="disabled">');
        $('.contDescHabitacion.with_rates_dropdown .room_from_price').show();
        $('.contDescHabitacion.with_rates_dropdown .room_from_price.club').hide();
        $('#rooms_b1_wrapper tr.rate_tr_element, #rooms_b1_wrapper tr.regimen_tr_element').show();
        $('#rooms_b1_wrapper tr.rate_tr_element.rate_for_users, #rooms_b1_wrapper tr.regimen_tr_element.regimen_for_users').hide();
    }

    function performBookingSearchWithClubDiscountState(choice_club_discount) {
        const booking_form = $('form.paraty-booking-form');
        booking_form.find('input[name="choice_club_discount"]').remove();
        booking_form.append(`<input type="hidden" name="choice_club_discount" value="${choice_club_discount}">`);

        ClubController.perform_booking_search();
    }

    function sendEventToggleDiscount(activated) {
        const creative_name = activated ? 'discount_activated' : 'discount_disabled';

        typeof gtag !== 'undefined' && gtag("event", "view_promotion", {
          creative_name: creative_name,
          creative_slot: "discount_checkbox",
          promotion_id: "Club discount",
          promotion_name: "Club discount",
          items: [
            {
              item_id: "Club discount",
            }
          ]
        });
    }

    function recoverPassword(username, language) {
        const params_post = {
            'action': 'recovery',
            'email': username,
            'language': language,
            'namespace': $("input[name='namespace']").val(),
        };

        $.post('/users/', params_post, function(){
            let recover_password_message = null;
            if (typeof $.i18n == 'object') {
                recover_password_message = $.i18n._("T_correo_enviado");
            } else {
                recover_password_message = "Email enviado";
            }

            alert(recover_password_message);
        });
    }
</script>
