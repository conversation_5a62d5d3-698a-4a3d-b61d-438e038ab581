# -*- coding: utf-8 -*-
import datetime
import os

from booking_process.constants.advance_configs_names import CLOSED_HOTEL
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT, VISUAL_DATE_FORMAT
from booking_process.utils.calendar.utilities.closed_hotel_utils import get_range_hotel_closed
from booking_process.utils.prices.price_context import _get_prices_separators
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.constants.advance_configs_names import SHOW_BABIES, CUSTOM_PRICE_FORMAT
from utils.flask_requests import response_utils
from utils.web.BaseInjectionHandler import InjectionScript, InjectionWidgetHandler


class TaferScript(InjectionScript):

    def params_base_script_controller(self):
        namespace_map = {
            'tafer-garza-blanca': 'garza',
            'tafer-lemuria': 'lemuria',
            'tafer-palmar-azul': 'palmar'
        }
        namespace = get_namespace()
        prefix = namespace_map.get(namespace)

        context = {
            "widget_url": f"{prefix}widget",
            "widget_css": f"{prefix}_injection_styles",
            "static_version": "1.15",
            "booking_version": "7-min",
            "calendar_version": "5"
        }

        return context

    def template_controller_name(self):
        return "tafer_widget_controller.js"

    def template_controller_path(self):
        return os.path.join(os.path.dirname(__file__), self.template_controller_name())


class TaferWidgetHandler(InjectionWidgetHandler):

    def get(self, *args):
        headers = response_utils.get_response_headers()
        headers['Access-Control-Allow-Origin'] = '*'
        headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, X-Auth-Token, X-CSRF-Token'
        response_utils.add_response_headers(headers)
        return super(InjectionWidgetHandler, self).get(*args)

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(TaferWidgetHandler, self).getBookingWidgetOptions(language)
        options['custom_promocode_label'] = get_web_dictionary(language).get('T_promocode')

        widget_config = get_section_from_section_spanish_name_with_properties("_widget_config", language)
        if widget_config:
            if widget_config.get('extra_widget_class'):
                options['ExtraElementBeforeRoomList'] = f'<input type="hidden" id="extra_widget_class" value="{widget_config["extra_widget_class"]}"/>'

            mini_dict = dict(get_web_dictionary(language))
            widget_pics = get_pictures_from_section_name("_widget_config", language)
            mini_dict['widget_buttons'] = []
            for button in widget_pics:
                if button.get('title') == 'button':
                    button.update(get_properties_for_entity(button.get('key'), language))
                    mini_dict['widget_buttons'].append(button)

            logotype = list(filter(lambda x: x.get('title') == 'logo', widget_pics))
            if logotype:
                mini_dict['logo'] = logotype[0]

            mini_dict['booking_steps'] = True
            if get_config_property_value(CLOSED_HOTEL):
                closed_hotel_days = get_range_hotel_closed()
                today = datetime.datetime.now()
                for period in closed_hotel_days:
                    open_day = datetime.datetime.strptime(period.get("open"), SEARCH_DATE_FORMAT)
                    close_day = datetime.datetime.strptime(period.get("close"), SEARCH_DATE_FORMAT)
                    if close_day <= today <= open_day:
                        startDate = open_day.strftime(VISUAL_DATE_FORMAT)
                        mini_dict['forcedStartDate'] = startDate
                        break

            options['custom_html_before_wrapper'] = self.buildTemplate_2("tafer/_widget_content.html", mini_dict, False)

        options['inline_ages'] = False
        options['namespace'] = ''

        options['caption_submit_book'] = True
        options['departure_date_select'] = True
        options['booking_no_hide'] = True
        options['showBabies'] = get_config_property_value(SHOW_BABIES)
        options['avoid_translations'] = True
        options['no_personalized_dates_selector'] = True

        prices_format_config = get_config_property_value(CUSTOM_PRICE_FORMAT)
        if prices_format_config:
            options['thousands_separator'], options['decimal_separator'] = _get_prices_separators(prices_format_config, language)

        return options
