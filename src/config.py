from hoteles.tafer.tafer_booking_widget import TaferScript, TaferWidgetHandler
from utils.web.web_seeker_utils import WebSeekerHandler

applicationId = "tafer-hotels"
template_handler = None

extra_routing = {
    '.*localhost.*': {
		'.*garzascript.*': TaferScript,
		'.*garzawidget.*': TaferWidgetHandler,
        '.*palmarscript.*': TaferScript,
		'.*palmarwidget.*': TaferWidgetHandler,
        '.*lemuriascript.*': TaferScript,
        '.*lemuriawidget.*': TaferWidgetHandler,
        '.*': WebSeekerHandler,
    },
    '.*garza.*': {
		'.*garzascript.*': TaferScript,
		'.*garzawidget.*': TaferWidgetHandler,
        '.*': WebSeekerHandler
    },
    '.*palmar.*': {
		'.*palmarscript.*': TaferScript,
		'.*palmarwidget.*': <PERSON>fer<PERSON>idget<PERSON>andler,
        '.*': WebSeekerHandler
    },
    '.*lemuria.*': {
        '.*lemuriascript.*': TaferScript,
        '.*lemuriawidget.*': TaferWidgetHandler,
        '.*': WebSeekerHandler
    },
    '.*': {
		'.*garzascript.*': TaferScript,
		'.*garzawidget.*': TaferWidgetHandler,
        '.*palmarscript.*': TaferScript,
		'.*palmarwidget.*': TaferWidgetHandler,
        '.*lemuriascript.*': TaferScript,
        '.*lemuriawidget.*': TaferWidgetHandler,
        '.*': WebSeekerHandler
    }
}

flexible_server_type = True
automatic_flexible_scaling = True
min_automatic_flex_instances = 1
max_automatic_flex_instances = 6
server_cpu = 1
server_memory = 4

CLOUD_RUN_MEMORY = '4Gi'
CLOUD_RUN_CPU = 1
CLOUD_RUN_CONCURRENCY = 20
CLOUD_RUN_WORKERS = 1
CLOUD_RUN_THREADS = 20

templates = []
