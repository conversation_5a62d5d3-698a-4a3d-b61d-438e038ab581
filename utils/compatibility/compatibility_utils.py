import json


def _clean_control_characters(element: str|dict|list|tuple):
    """
    Recursively clean control characters from all string values in a data structure.

    This function traverses dictionaries, lists, and other data structures to find
    string values and removes control characters that could break JSON parsing.

    :param element: The object to clean (can be dict, list, string, or any other type)
    :return: Cleaned object with control characters removed from strings
    """
    if isinstance(element, str):
        return element.translate(str.maketrans({
            '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' ',
            '\x00': '', '\x01': '', '\x02': '', '\x03': '', '\x04': '', '\x05': '', '\x06': '', '\x07': '',
            '\x08': '', '\x0b': '', '\x0c': '', '\x0e': '', '\x0f': '', '\x10': '', '\x11': '', '\x12': '',
            '\x13': '', '\x14': '', '\x15': '', '\x16': '', '\x17': '', '\x18': '', '\x19': '', '\x1a': '',
            '\x1b': '', '\x1c': '', '\x1d': '', '\x1e': '', '\x1f': ''
        })).strip()
    elif isinstance(element, dict):
        return {key: _clean_control_characters(value) for key, value in element.items()}
    elif isinstance(element, list):
        return [_clean_control_characters(item) for item in element]
    elif isinstance(element, tuple):
        return tuple(_clean_control_characters(item) for item in element)
    else:
        return element


def dumps_json_for_javascript(content):
    """
    Dumps JSON with proper escaping for use in JavaScript template literals in Django templates.

    This function double-escapes quotes and other special characters so that when the JSON
    is embedded in Django template literals like `JSON.parse(\`{{ json_data|safe }}\`)`,
    the resulting JavaScript receives properly escaped JSON that can be parsed correctly.

    Properly handles UTF-8 characters (like accented characters) preserving them in their
    original form without double-encoding issues. Also cleans control characters from all
    string values in the data structure to prevent JSON parsing errors.

    Note: The output is NOT valid JSON for Python's json.loads() due to double-escaped quotes.
    This is intentional, the double-escaping is consumed by JavaScript template literal processing.

    :param content: Content with JSON structure to dumps and clean.
    :return: JSON dumped with correct escaping for JS template literals.
    """

    # First, clean control characters from all string values in the data structure
    cleaned_content = _clean_control_characters(content)

    # Generate clean JSON - use ensure_ascii=False to preserve unicode characters
    dumped_content = json.dumps(cleaned_content, ensure_ascii=False, separators=(',', ':'))
    final_content = dumped_content.translate(str.maketrans({
        '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
    }))

    # In template literals, \" becomes " so we need \\" to get \" in the final JSON
    final_content = final_content.replace('\\"', '\\\\"')

    # We need to escape them as \\` to create a valid JSON string that contains \`
    final_content = final_content.replace('`', '\\\\`')

    return final_content
