import json
import logging
from math import inf

from booking_process.components.analytics.utils.analytics_constants import SEARCH_EVENT, BOOKING_EVENT, DENIAL_EVENT, \
	DUETTO, VIEW_ITEM_LIST
from booking_process.constants.advance_configs_names import MEMBERS_CLUB
from booking_process.utils.analytics.analyticsUtils import _get_spanish_rate_name
from booking_process.utils.analytics.ga4_utils import get_extra_params_by_event
from booking_process.utils.booking.common_data_provider import get_rate
from booking_process.utils.booking.results_management.results_general import get_all_available_price_options
from booking_process.constants.session_data import USER_LOGIN_INFO, LANGUAGE, SEARCH_KEY_PREFIX
from booking_process.utils.clubs.rates_management import get_related_rate_key_from_locked_rates_config
from booking_process.utils.currency.currencyUtils import get_selected_currency_code, get_currency
from booking_process.utils.data_management.boards_data import get_board
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.rooms_data import get_room
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.language.language_utils import get_analytics_language
from booking_process.utils.session import session_manager
from booking_process.utils.taxes.tax_utils import remove_tax_from_price
from booking_process.utils.users.users_methods import club_is_active, check_if_user_exists
from models.reservations import Reservation
from paraty_commons_3.security_utils import hash_password
from paraty_commons_3.utils.country.country_utils import get_prefix_country_list


def get_general_purchase_event_context(conversion_tag: str, reservation: Reservation) -> dict:
	"""
	Builds general context to purchase event.
	:param conversion_tag: Event send to it.
	:param reservation: Reservation to send.
	:return: General context.
	"""
	price_currency = get_currency()

	try:
		reservation_extra_info = reservation.extraInfo
		extra_info = json.loads(reservation_extra_info)
		price_currency = extra_info.get('currency') or price_currency
	except Exception as e:
		logging.warning("Error getting currency from reservation extra info: %s", e)

	return {
		'conversion_label': conversion_tag,
		'account_identifier': conversion_tag.split("/")[0] if '/' in conversion_tag else '',
		'identifier': reservation.identifier,
		'total_price': reservation.price,
		'selected_currency': price_currency
	}


def hash_analytics_params(context: dict, params: set[str]) -> dict:
	hashed_params = {}
	for param in params:
		current_param_value = context.get(param) or ''

		if current_param_value:
			current_param_value = current_param_value.replace(' ', '').lower()

			if 'phone_number' in param:
				current_param_value = normalize_phone_number(context, current_param_value)

		hashed_params[f"{param}_sha256"] = hash_password(current_param_value) if current_param_value else None

	return hashed_params


def normalize_phone_number(context: dict, phone_number: str) -> str:
	if phone_number.startswith('+'):
		return phone_number

	country_selected = context.get('country') or context.get('pais')
	if not country_selected:
		return phone_number

	prefix_country_list = get_prefix_country_list(session_manager.get(LANGUAGE))
	prefix = next(
		(prefix_dict.get('dial_code', '')
		 for prefix_dict in prefix_country_list
		 if country_selected == prefix_dict.get('code', '')),
		''
	)

	return f"{prefix}{phone_number}"


def get_dueto_context(
		event: str,
		reservation: Reservation | None = None
) -> dict:
	"""
	    Function to build duetto context.

	    :param event: Type of  event to send.
	    :param reservation: Reservation.
	    :return: A dict with the necessary context for duetto templates.
	    """

	currency = get_currency()
	language = get_analytics_language()
	duetto = get_web_configuration(DUETTO)
	search = session_manager.get(SEARCH_KEY_PREFIX) or {}
	results = get_all_available_price_options(language)

	context = {
		'duetto_app_id': duetto.get('app_id'),
		'duetto_hotel_id': duetto.get('hotel_id'),
		'duetto_tld': duetto.get('tld'),
		'duetto_script_url': duetto.get('script_url'),
		'duetto_event': event
	}

	if search:
		context['duetto_arrival'] = search.get('startDate')
		context['duetto_departure'] = search.get('endDate')

	if event == SEARCH_EVENT:
		context.update(_get_duetto_results(results, currency, language, duetto.get('results_list_length')))

	if event == BOOKING_EVENT:
		context.update(_get_duetto_reservation(reservation, currency, language))

	return context


def _get_duetto_results(results, currency, language, list_length = None):
	min_price = float(inf)
	max_price = 0
	quotes = []
	context = {}

	if not results:
		context['duetto_event'] = DENIAL_EVENT
		return context

	try:
		results_list = sorted(results, key=lambda x: x.get('price'))

		if list_length:
			results_list = results_list[:int(list_length)]

		for price in results_list:
			room_key = price.get('room_key')
			rate_key = price.get('rate_key')
			price = price.get('price')

			room = get_room(room_key, language)
			room_type = unescape(room.get('room_name', '')).strip()
			rate = get_rate(rate_key, language)
			rate_cod = unescape(rate.get('localName', ''))

			if price > max_price:
				max_price = price
			if price < min_price:
				min_price = price

			quote = {
				'room_type':room_type,
				'rate_cod': rate_cod,
				'rate': price,
				'currency': currency
			}
			quotes.append(quote)

	except Exception as e:
		logging.warning("Error getting duetto room list")

	context['duetto_min_price'] = min_price
	context['duetto_max_price'] = max_price
	context['duetto_quotes'] = quotes

	return context


def _get_duetto_reservation(reservation, currency, language):

	retreive_total_adults = lambda booking_object: sum(
		[int(z) for z in [x for x in [0, booking_object.adults1, booking_object.adults2, booking_object.adults3] if x]])
	retreive_total_kids = lambda booking_object: sum(
		[int(z) for z in [x for x in [0, booking_object.kids1, booking_object.kids2, booking_object.kids3] if x]])

	room = get_room(reservation.roomType1, language)
	room_type = unescape(room.get('room_name'))
	rate_cod = _get_spanish_rate_name(reservation)
	context = {
		'duetto_confirmation_id': reservation.identifier,
		'duetto_room_type': room_type,
		'duetto_rate_cod': rate_cod,
		'duetto_rate': remove_tax_from_price(reservation.price),
		'duetto_currency': currency,
		'duetto_num_adults': retreive_total_adults(reservation),
		'duetto_num_children': retreive_total_kids(reservation),
		'duetto_arrival': reservation.startDate,
		'duetto_departure': reservation.endDate
	}

	return context


def get_reduced_view_item_list_context() -> dict:
	currency = get_currency()
	language = get_analytics_language()

	results = get_all_available_price_options(language)
	room_results = [room for room in results if not room.get('package_name')]
	room_keys = set([room.get('room_key') for room in room_results])
	cheapest_rooms = []

	for room_key in room_keys:
		try:
			same_rooms_no_club = [room for room in room_results if room.get('room_key') == room_key and not room.get('is_club_rate')]
			same_rooms_no_club.sort(key=lambda x: x.get('price'))

			# Handle case where all rooms are club rates
			if not same_rooms_no_club:
				logging.info("All rooms for room_key %s are club rates, using cheapest available room as reference", room_key)
				same_rooms_all = [room for room in room_results if room.get('room_key') == room_key]
				same_rooms_all.sort(key=lambda x: x.get('price'))
				cheapest_room_reference = same_rooms_all[0] if same_rooms_all else None
			else:
				cheapest_room_reference = same_rooms_no_club[0]

			if not cheapest_room_reference:
				logging.warning("No rooms found for room_key %s", room_key)
				continue

			alt_rate = get_related_rate_key_from_locked_rates_config('active', cheapest_room_reference.get('rate_key'))

			cheapest_club_rooms = [
				room for room in room_results
				if room.get('room_key') == cheapest_room_reference.get('room_key')
				and room.get('board_key') == cheapest_room_reference.get('board_key')
				and room.get('rate_key') == alt_rate
			]
			if cheapest_club_rooms and cheapest_club_rooms[0]:
				cheapest_rooms.append(cheapest_club_rooms[0])
		except Exception as e:
			logging.warning("Error processing room_key %s in get_reduced_view_item_list_context: %s", room_key, e)

	cheapest_rooms.sort(key=lambda x: x.get('price'))

	context = {
		'rooms': cheapest_rooms,
		'currency': currency,
		'extra': get_extra_params_by_event(language).get(VIEW_ITEM_LIST, {})
	}

	return context