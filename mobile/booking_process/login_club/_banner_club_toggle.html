<link href="/static_1/css/utils/users/club_toggle_mobile.css?v=@@automatic_version@@" rel="stylesheet" />

<style>
    {% if club_config %}
        {% if club_config.color1 %}
            :root {
                --club-main-color: {{ club_config.color1|safe }};
            }
        {% endif %}

        {% if club_config.color2 %}
            :root {
                --club-secondary-color: {{ club_config.color2|safe }}!important;
            }
        {% endif %}

        {% if club_config.color_text %}
            :root {
                --club-text-color: {{ club_config.color_text|safe }}!important;
            }
        {% endif %}
    {% endif %}
</style>

<div class="banner-toggle-club">
    {% if banner_club %}
        <div class="logotype-wrapper">
            <img src="{{ banner_club.servingUrl }}" alt="logotype">
        </div>
    {% endif %}

    <div class="main-content">
        <a class="see-advantages"><i class="fa-light fa-circle-info" aria-hidden="true"></i></a>
        <span class="main-text">
            {{ main_description.description|safe }}
        </span>
    </div>

    <div class="toggle-discount-wrapper {% if disable_club_discount and is_booking3 %}discount-disabled{% endif %}"><span class="label-text">{{ T_descuento_activo }}</span><span
            class="label-text label-disabled-text">{{ T_descuento_inactivo }}</span>
        <div class="discount-switch-wrapper">
            <div class="switch">
                <input class="checkbox" type="checkbox" checked>
                <div class="knobs"></div>
                <div class="layer"></div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        $(document).on("change", ".banner-toggle-club .discount-switch-wrapper input.checkbox", function () {
            const $roomBookingForm = $("form.room_booking_form");
            const $checkboxDiscount = $(this);
            const discountStatus = $checkboxDiscount.is(':checked') ? "active" : "disabled";

            $roomBookingForm.find('input[name="choice_club_discount"]').val(discountStatus);
        });
    });

    $(function() {
        const checkboxDiscount = $('.banner-toggle-club .discount-switch-wrapper input.checkbox');

        $('.room_list .room_rates_list .discount_disabled, .booking_process_mobile_v1 .lock_rates_wrapper').click(function() {
            checkboxDiscount.prop('checked', 'checked').trigger('change');
        });

        checkboxDiscount.change(function () {
            const $checkbox = $(this);
            const is_checked = $checkbox.is(':checked');
            const loading_popup = $(".loading_animation_popup");

            if (loading_popup.length) {
                let loading_popup_content = loading_popup.html();

                if (loading_popup_content) {
                    const selected_start_date = $("input[name='startDate']").val();
                    const selected_end_date = $("input[name='endDate']").val();

                    if (selected_start_date) {
                        loading_popup_content = loading_popup_content.replace('@@start_date@@', selected_start_date);
                    }

                    if (selected_end_date) {
                        loading_popup_content = loading_popup_content.replace('@@end_date@@', selected_end_date);
                    }

                    loading_popup.html(loading_popup_content);
                }
            }

            sendEventToggleDiscount(is_checked);
            toggleDiscount();

            const current_url = window.location.href;
            const choice_club_discount = is_checked ? 'active' : 'disabled';

            if (current_url.includes('/booking3')) {
                // Reload page with URL parameter
                const url = new URL(current_url);
                const params = new URLSearchParams(url.search);

                if (params.has('choice_club_discount')) {
                    params.set('choice_club_discount', choice_club_discount);
                } else {
                    params.append('choice_club_discount', choice_club_discount);
                }

                if (typeof openBookingSearchPopup_v2 !== 'undefined') {
                    openBookingSearchPopup_v2();
                } else {
                    iOS() ? loading_popup.show() : loading_popup.fadeIn();
                    setTimeout(function () {
                        loading_popup.fadeOut();
                    }, 7500);
                }

                url.search = params.toString();
                window.location.href = url.toString();
            } else if (current_url.includes('/booking1')) {
                if ($("input[name='selectedUUID']").val()) {
                    if (confirm("Se va a recargar la búsqueda. ¿Desea continuar?")) {
                        performBookingSearchWithClubDiscountState(choice_club_discount);
                    } else {
                        // If the user doesn't want to reload the page, we need to revert the checkbox state.
                        toggleDiscount();
                        $checkbox.prop('checked', !is_checked);
                    }
                } else {
                    // Maintain the club discount status
                    $("form.room_booking_form").find('input[name="choice_club_discount"]').val(choice_club_discount);

                    // Perform changes without leaving the page
                }
            } else {
                // Repeat booking search with parameter
                performBookingSearchWithClubDiscountState(choice_club_discount);
            }
        });

        $(window).on('rooms-reset', function() {
            setValidRates();
        });

        setValidRates();
    });

    function setValidRates() {
        {% if disable_club_discount %}
            const checkboxDiscount = $('.banner-toggle-club .discount-switch-wrapper input.checkbox');
            checkboxDiscount.prop('checked', false);
            disableClubDiscount();
            {% if not is_booking3 %}
                checkboxDiscount.trigger('change');
            {% endif %}
        {% else %}
            activateClubDiscount();
        {% endif %}
    }

    function toggleDiscount() {
        $('.toggle-discount-wrapper').toggleClass('discount-disabled');
        $('.room_list .room_rates_list .rates_details_wrapper').toggle();
    }

    function activateClubDiscount() {
        $('.room_booking_form input[name="choice_club_discount"]').remove();
        $('.room_booking_form').append('<input type="hidden" name="choice_club_discount" value="active">');
        $('.room_from_price_wrapper  .room_from_price').css('display', 'none');
        $('.room_from_price_wrapper  .room_from_price.club').css('display', 'block');
        $('.room_list .room_rates_list .rates_details_wrapper').css('display', 'none');
        $('.room_list .room_rates_list .rates_details_wrapper.only_logged').css('display', 'block');
    }

    function disableClubDiscount() {
        $('.room_booking_form input[name="choice_club_discount"]').remove();
        $('.room_booking_form').append('<input type="hidden" name="choice_club_discount" value="disabled">');
        $('.room_from_price_wrapper  .room_from_price').css('display', 'block');
        $('.room_from_price_wrapper  .room_from_price.club').css('display', 'none');
        $('.room_list .room_rates_list .rates_details_wrapper').css('display', 'block');
        $('.room_list .room_rates_list .rates_details_wrapper.only_logged').css('display', 'none');
    }

    function performBookingSearchWithClubDiscountState(choice_club_discount) {
        const booking_form = $('form.paraty-booking-form');
        booking_form.find('input[name="choice_club_discount"]').remove();
        booking_form.append(`<input type="hidden" name="choice_club_discount" value="${choice_club_discount}">`);

        ClubController.perform_booking_search();
    }

    function sendEventToggleDiscount(activated) {
        const creative_name = activated ? 'discount_activated' : 'discount_disabled';

        typeof gtag !== 'undefined' && gtag("event", "view_promotion", {
          creative_name: creative_name,
          creative_slot: "discount_checkbox",
          promotion_id: "Club discount",
          promotion_name: "Club discount",
          items: [
            {
              item_id: "Club discount",
            }
          ]
        });
    }
</script>
